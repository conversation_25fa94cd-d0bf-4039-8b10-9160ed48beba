# STM32 GPS+GSM 数据采集与传输系统

## 项目概述

这是一个基于STM32微控制器的低功耗数据采集与传输系统，主要用于GPS定位数据和传感器数据的采集、处理和远程传输。系统采用FreeRTOS实时操作系统，支持GPS定位、GSM网络通信、传感器数据采集和智能电源管理。

## 主要功能

### 1. GPS定位系统
- **高精度定位**：支持GPS模块数据解析，获取经纬度、海拔、卫星数量等信息
- **时钟同步**：自动使用GPS时间同步RTC时钟
- **智能等待**：首次启动等待5分钟，正常工作等待2分钟 使用两个时间宏定义
#define GPS_TIMEOUT_NORMAL 120        // GPS常规定位等待时间
#define GPS_TIMEOUT_FIRST_BOOT 300    // GPS初次启动等待时间
- **故障恢复**：连续3次失败后自动重启GPS模块

### 2. GSM网络通信
- **TCP连接**：自动连接到指定服务器(47.117.43.95:48085)
- **数据传输**：实时发送GPS和传感器数据到服务器
- **指令接收**：支持接收服务器下发的ZL+系列控制指令
- **历史数据**：发送失败的数据自动存储，成功连接后补发

### 3. 传感器数据采集
- **姿态传感器**：LSM6DS3六轴传感器，获取Roll/Pitch角度
- **电池监测**：实时监测电池电压
- **温度监测**：MCU内部温度传感器

### 4. 智能电源管理
- **低功耗休眠**：支持STOP模式，功耗极低
- **动态休眠时间**：支持网络指令动态调整休眠时间
- **工作时间控制**：支持设置工作时间段，非工作时间智能休眠 （当次收到设定时直接修改休眠时间到工作时间内）
- **电源隔离**：各模块独立电源控制，避免相互干扰

### 5. 数据存储系统
- **SPI Flash存储**：使用W25Q64芯片存储历史数据
- **环形缓冲区**：自动覆盖最旧数据，确保存储空间高效利用
- **数据完整性**：支持数据校验和恢复

## 系统架构

### 硬件架构
```
STM32L476RG (主控)
├── GPS模块 (UART1)
├── GSM模块 (LPUART1)
├── LSM6DS3传感器 (I2C1)
├── W25Q64 Flash (SPI1)
├── RTC时钟
└── 电源管理电路
```

### 软件架构
```
FreeRTOS
├── GPS任务 (StartGPSTask)
├── GSM任务 (StartGSMTask)
├── 传感器任务 (StartAccelTask)
├── Flash任务 (StartFlashTask)
└── 主控任务 (StartPowerTask)
```

## 代码结构

### 核心文件
- **`Src/freertos.c`** - FreeRTOS任务管理和主控逻辑
- **`Src/main.c`** - 系统初始化和中断处理
- **`Src/GSM.c`** - GSM模块AT指令库
- **`Src/GPS.c`** - GPS数据解析和处理
- **`Src/gpio.c`** - GPIO和电源管理
- **`Src/spi_flash.c`** - SPI Flash存储管理

### 功能模块
- **`Src/rtc_sync.c`** - RTC时钟同步
- **`Src/data_synthesis.c`** - 数据合成和格式化
- **`Src/work_time.c`** - 工作时间管理
- **`Src/GM20.c`** - GM20模块支持(备用)

### 配置文件
- **`Inc/globals.h`** - 全局变量和配置
- **`Inc/GSM.h`** - GSM模块接口定义
- **`Inc/GPS.h`** - GPS模块接口定义

## 工作流程

### 1. 系统启动
1. 硬件初始化 (时钟、GPIO、UART、I2C、SPI)
2. FreeRTOS任务创建
3. 电源管理初始化
4. 进入主循环

### 2. 数据采集周期
1. **GPS任务**：开启GPS电源 → 等待定位 → 同步时钟 → 关闭电源
2. **传感器任务**：读取LSM6DS3数据 → 计算姿态角度
3. **等待GPS完成**：使用同步标志位确保GPS任务完全结束
4. **GSM任务**：开启GSM电源 → 连接服务器 → 准备数据传输

### 3. 数据传输
1. 合成数据字符串 (GPS+传感器+系统信息)（第一次启动时也要提取GSM的信号值和CCID号）
2. GSM发送数据到服务器
3. 等待服务器确认ZL+指令
4. 发送成功：继续下一步；失败：存储到Flash

### 4. 休眠管理
1. 关闭所有外设电源
2. 进入STOP低功耗模式
3. RTC定时唤醒或外部中断唤醒
4. 唤醒后重新初始化外设

## 网络指令系统

系统支持接收服务器下发的ZL+系列指令：

ZL+S10+F16E16+N20

ZL+ 自定义指令标识
D 表示 天  设备休眠时间设置
H 表示 时  设备休眠时间设置
M 表示 分  设备休眠时间设置
S 表示 秒  设备休眠时间设置
N 表示 打包N条数据一起发送  （只接收 暂时不用）
F 表示 工作时间段设置标识，F表示开始时间 E 表示结束时间 示例：ZL+F16E22 表示工作时间段是16点——22点 内工作
收到休眠时间和工作时间段设定当次就要设置生效

## 数据格式

### 发送数据格式
```
HY<序号><状态>+<纬度>+<经度>+<时间>+<温度>+<定位质量>+<卫星数>+<速度>+<电压>+<MCU温度>+<湿度>+<CO2>+<Roll>+<Pitch>+<航向>+<HDOP>+<信号强度>+<CCID>+E
```

### 示例
```
HY110S+119.96203+30.27616+050439.110725+46.0+1+8+1.3+6.72+28.1+1.5+3.0+-0.6+-1.4+0.0+0.00+31+89430103223249458271+E
```

## 编译和部署

### 开发环境
- **IDE**: STM32CubeIDE
- **芯片**: STM32L476RG
- **RTOS**: FreeRTOS
- **HAL库**: STM32L4xx HAL Driver

### 编译步骤
1. 打开STM32CubeIDE
2. 导入项目
3. 配置编译器 (ARM GCC)
4. 编译生成.bin/.hex文件
5. 使用ST-Link下载到目标板

## 注意事项

### 1. 电源管理
- GPS和GSM模块不能同时工作，避免UART中断冲突
- 休眠前必须关闭所有外设电源
- 硬件缺陷已修复，使用正常输出模式控制电源：高电平关闭、低电平开启

### 2. 时序控制
- GPS任务必须完全结束后才能启动GSM任务
- 使用同步标志位 `gps_task_completed` 和 `gsm_can_start` 控制时序
- 延时函数使用 `osDelay()` 而非 `HAL_Delay()`

### 3. 数据传输
- 发送成功的标准是收到服务器`ZL+` 指令
- `DATA ACCEPT:xxx` 只是GSM模块确认，不代表服务器收到
- 失败的数据会自动存储到Flash，下次连接时补发（合成数据时同时合成S和B数据备用，发送S失败就保存B数据）

### 4. 调试建议
- 使用串口输出调试信息
- 监控电流表观察电源管理效果
- 检查服务器日志确认数据接收情况

## 版本历史

- **v1.0** - 基础GPS+GSM功能
- **v1.1** - 添加FreeRTOS多任务支持
- **v1.2** - 完善电源管理和时序控制
- **v1.3** - 修复ACCEPT判断逻辑，优化数据传输可靠性

## 联系信息

如有问题或建议，请联系项目维护者。
